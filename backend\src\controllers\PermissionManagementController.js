const db = require('../config/database');

/**
 * Permission Management Controller
 * Handles UI-based permission management for roles and modules
 */

// Get complete modules tree with permissions
exports.getModulesTree = async (req, res) => {
  try {
    console.log('PermissionManagementController - Getting modules tree');

    // Get all modules with their submodules and permissions
    const query = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Description as SubModuleDescription,
        sm.Route as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.DisplayOrder as SubModuleDisplayOrder,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.DisplayOrder, p.Name
    `;

    const result = await db.query(query);

    // Transform flat result into hierarchical structure
    const modulesMap = new Map();

    result.recordset.forEach(row => {
      // Create module if not exists
      if (!modulesMap.has(row.ModuleId)) {
        modulesMap.set(row.ModuleId, {
          id: row.ModuleId,
          name: row.ModuleName,
          description: row.ModuleDescription,
          icon: row.ModuleIcon,
          displayOrder: row.ModuleDisplayOrder,
          isActive: row.ModuleIsActive,
          subModules: new Map()
        });
      }

      const module = modulesMap.get(row.ModuleId);

      // Create submodule if not exists and if SubModuleId is not null
      if (row.SubModuleId && !module.subModules.has(row.SubModuleId)) {
        module.subModules.set(row.SubModuleId, {
          id: row.SubModuleId,
          name: row.SubModuleName,
          description: row.SubModuleDescription,
          route: row.SubModuleRoute,
          icon: row.SubModuleIcon,
          displayOrder: row.SubModuleDisplayOrder,
          isActive: row.SubModuleIsActive,
          permissions: []
        });
      }

      // Add permission if exists
      if (row.PermissionId && row.SubModuleId) {
        const subModule = module.subModules.get(row.SubModuleId);
        if (subModule && !subModule.permissions.find(p => p.id === row.PermissionId)) {
          subModule.permissions.push({
            id: row.PermissionId,
            name: row.PermissionName,
            description: row.PermissionDescription,
            subModulePermissionId: row.SubModulePermissionId
          });
        }
      }
    });

    // Convert Maps to Arrays for JSON response
    const modules = Array.from(modulesMap.values()).map(module => ({
      ...module,
      subModules: Array.from(module.subModules.values())
    }));

    res.json({
      success: true,
      data: modules,
      message: 'Modules tree retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getModulesTree:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get all roles
exports.getRoles = async (req, res) => {
  try {
    console.log('PermissionManagementController - Getting all roles');

    const query = `
      SELECT 
        Id,
        Name,
        IsActive,
        CreatedOn
      FROM Roles
      WHERE IsActive = 1
      ORDER BY Name
    `;

    const result = await db.query(query);

    res.json({
      success: true,
      data: result.recordset,
      message: 'Roles retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getRoles:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get permissions for a specific role
exports.getRolePermissions = async (req, res) => {
  try {
    const roleId = req.params.roleId;
    console.log('PermissionManagementController - Getting permissions for role:', roleId);

    const query = `
      SELECT 
        rp.Id as RolePermissionId,
        rp.RoleId,
        rp.SubModulePermissionId,
        rp.IsActive as RolePermissionIsActive,
        
        smp.Id as SubModulePermissionId,
        smp.SubModuleId,
        smp.PermissionId,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.ModuleId,
        
        m.Id as ModuleId,
        m.Name as ModuleName,
        
        p.Id as PermissionId,
        p.Name as PermissionName
        
      FROM RolePermissions rp
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE rp.RoleId = @roleId AND rp.IsActive = 1
      ORDER BY m.DisplayOrder, sm.DisplayOrder, p.Name
    `;

    const result = await db.query(query, { roleId: parseInt(roleId) });

    res.json({
      success: true,
      data: result.recordset,
      message: 'Role permissions retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getRolePermissions:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Update role permissions
exports.updateRolePermissions = async (req, res) => {
  try {
    const roleId = req.params.roleId;
    const { permissions } = req.body; // Array of { subModulePermissionId, isActive }
    const modifiedBy = req.user.id;

    console.log('PermissionManagementController - Updating permissions for role:', roleId);
    console.log('Permissions to update:', permissions);

    // Start transaction
    const transaction = await db.beginTransaction();

    try {
      // Process each permission update
      for (const permission of permissions) {
        const { subModulePermissionId, isActive } = permission;

        // Check if role permission already exists
        const existingQuery = `
          SELECT Id FROM RolePermissions 
          WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
        `;
        
        const existingResult = await db.query(existingQuery, {
          roleId: parseInt(roleId),
          subModulePermissionId: parseInt(subModulePermissionId)
        });

        if (existingResult.recordset.length > 0) {
          // Update existing permission
          await db.query(`
            UPDATE RolePermissions 
            SET IsActive = @isActive, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
            WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
          `, {
            roleId: parseInt(roleId),
            subModulePermissionId: parseInt(subModulePermissionId),
            isActive: isActive ? 1 : 0,
            modifiedBy: parseInt(modifiedBy)
          });
        } else if (isActive) {
          // Create new permission if it doesn't exist and isActive is true
          await db.query(`
            INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@roleId, @subModulePermissionId, 1, @createdBy, GETDATE())
          `, {
            roleId: parseInt(roleId),
            subModulePermissionId: parseInt(subModulePermissionId),
            createdBy: parseInt(modifiedBy)
          });
        }
      }

      // Commit transaction
      await transaction.commit();

      res.json({
        success: true,
        message: 'Role permissions updated successfully'
      });

    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Error in updateRolePermissions:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get permission matrix for all roles
exports.getPermissionMatrix = async (req, res) => {
  try {
    console.log('PermissionManagementController - Getting permission matrix');

    const query = `
      SELECT 
        r.Id as RoleId,
        r.Name as RoleName,
        
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.DisplayOrder as ModuleDisplayOrder,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.DisplayOrder as SubModuleDisplayOrder,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        
        smp.Id as SubModulePermissionId,
        
        CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as HasPermission
        
      FROM Roles r
      CROSS JOIN Modules m
      CROSS JOIN SubModules sm
      CROSS JOIN Permissions p
      CROSS JOIN SubModulePermissions smp
      LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId AND smp.Id = rp.SubModulePermissionId AND rp.IsActive = 1
      WHERE r.IsActive = 1 
        AND m.IsActive = 1 
        AND sm.IsActive = 1 
        AND sm.ModuleId = m.Id
        AND smp.SubModuleId = sm.Id
        AND smp.PermissionId = p.Id
        AND smp.IsActive = 1
      ORDER BY r.Name, m.DisplayOrder, sm.DisplayOrder, p.Name
    `;

    const result = await db.query(query);

    res.json({
      success: true,
      data: result.recordset,
      message: 'Permission matrix retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getPermissionMatrix:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

const db = require('../config/database');
const sql = require('mssql');

// Get all plaza valet points
exports.getAllPlazaValetPoints = async (req, res) => {
  try {
    const { 
      plazaId, 
      companyId, 
      isActive, 
      pageNumber = 1, 
      pageSize = 50, 
      searchTerm 
    } = req.query;

    let query = `
      SELECT 
        pvp.Id,
        pvp.PlazaId,
        pvp.ValetPointName,
        pvp.IsActive,
        pvp.CreatedBy,
        pvp.CreatedOn,
        pvp.ModifiedBy,
        pvp.ModifiedOn,
        pvp.CompanyId,
        pvp.Latitude,
        pvp.Longitude,
        p.Plaza<PERSON>ame,
        p.PlazaCode,
        c.CompanyName
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE 1=1
    `;
    const queryParams = {};

    // Apply role-based filtering
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only see valet points from companies they have access to
        query += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        // PlazaManager can only see valet points from plazas they have access to
        query += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    // Apply filters
    if (plazaId) {
      query += ` AND pvp.PlazaId = @plazaId`;
      queryParams.plazaId = plazaId;
    }

    if (companyId) {
      query += ` AND pvp.CompanyId = @companyId`;
      queryParams.companyId = companyId;
    }

    if (isActive !== undefined) {
      query += ` AND pvp.IsActive = @isActive`;
      queryParams.isActive = isActive === 'true' ? 1 : 0;
    }

    if (searchTerm) {
      query += ` AND pvp.ValetPointName LIKE @searchTerm`;
      queryParams.searchTerm = `%${searchTerm}%`;
    }

    query += ` ORDER BY pvp.CreatedOn DESC`;

    const result = await db.query(query, queryParams);

    res.json({
      success: true,
      valetPoints: result.recordset,
      message: 'Plaza valet points retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getAllPlazaValetPoints controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get plaza valet point by ID
exports.getPlazaValetPointById = async (req, res) => {
  try {
    const id = req.params.id;

    let query = `
      SELECT 
        pvp.Id,
        pvp.PlazaId,
        pvp.ValetPointName,
        pvp.IsActive,
        pvp.CreatedBy,
        pvp.CreatedOn,
        pvp.ModifiedBy,
        pvp.ModifiedOn,
        pvp.CompanyId,
        pvp.Latitude,
        pvp.Longitude,
        p.PlazaName,
        p.PlazaCode,
        c.CompanyName
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE pvp.Id = @id
    `;
    const queryParams = { id };

    // Apply role-based filtering
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        query += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        query += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    const result = await db.query(query, queryParams);

    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Plaza valet point not found or access denied'
      });
    }

    res.json({
      success: true,
      valetPoint: result.recordset[0],
      message: 'Plaza valet point retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getPlazaValetPointById controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Create plaza valet point
exports.createPlazaValetPoint = async (req, res) => {
  try {
    const {
      PlazaId,
      ValetPointName,
      IsActive = true,
      CompanyId,
      Latitude,
      Longitude
    } = req.body;

    // Validate required fields
    if (!PlazaId || !ValetPointName || !CompanyId) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: PlazaId, ValetPointName, and CompanyId are required'
      });
    }

    // Check if user has access to the plaza/company
    if (req.user.role !== 'SuperAdmin') {
      let accessQuery = '';
      const accessParams = { userId: req.user.id };

      if (req.user.role === 'CompanyAdmin') {
        accessQuery = `
          SELECT COUNT(*) as count
          FROM UserCompany uc
          JOIN Plaza p ON uc.CompanyId = p.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1
          AND p.Id = @plazaId AND p.CompanyId = @companyId
        `;
        accessParams.plazaId = PlazaId;
        accessParams.companyId = CompanyId;
      } else if (req.user.role === 'PlazaManager') {
        accessQuery = `
          SELECT COUNT(*) as count
          FROM UserPlaza up
          WHERE up.UserId = @userId AND up.IsActive = 1
          AND up.PlazaId = @plazaId
        `;
        accessParams.plazaId = PlazaId;
      }

      if (accessQuery) {
        const accessResult = await db.query(accessQuery, accessParams);
        if (accessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'Access denied: You do not have permission to create valet points for this plaza'
          });
        }
      }
    }

    // Get current user ID for CreatedBy
    const createdBy = req.user.id;

    // Log the data being sent to the stored procedure
    const spParams = {
      PlazaId,
      ValetPointName,
      IsActive: IsActive ? 1 : 0,
      CreatedBy: createdBy,
      CompanyId,
      Latitude: Latitude || null,
      Longitude: Longitude || null
    };

    console.log('PlazaValetPointController - Creating valet point with params:', spParams);

    // Use stored procedure to create valet point
    const result = await db.query(`
      DECLARE @NewId INT;
      EXEC [dbo].[sp_PlazaValetPoint_Create]
        @PlazaId = @PlazaId,
        @ValetPointName = @ValetPointName,
        @IsActive = @IsActive,
        @CreatedBy = @CreatedBy,
        @CompanyId = @CompanyId,
        @Latitude = @Latitude,
        @Longitude = @Longitude,
        @NewId = @NewId OUTPUT;
      SELECT @NewId AS NewId;
    `, spParams);

    console.log('PlazaValetPointController - Stored procedure result:', result);

    // Check if the stored procedure returned an error
    const spResult = result.recordset[0];
    if (spResult && spResult.Success === 0) {
      return res.status(400).json({
        success: false,
        message: spResult.ErrorMessage || 'Failed to create plaza valet point'
      });
    }

    const newId = result.recordset[0]?.NewId;

    if (!newId || newId === -1) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create plaza valet point'
      });
    }

    res.status(201).json({
      success: true,
      message: 'Plaza valet point created successfully',
      valetPointId: newId
    });
  } catch (error) {
    console.error('Error in createPlazaValetPoint controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Update plaza valet point
exports.updatePlazaValetPoint = async (req, res) => {
  try {
    const id = req.params.id;
    const {
      PlazaId,
      ValetPointName,
      IsActive,
      CompanyId,
      Latitude,
      Longitude
    } = req.body;

    // Validate required fields
    if (!PlazaId || !ValetPointName || !CompanyId) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: PlazaId, ValetPointName, and CompanyId are required'
      });
    }

    // Check if valet point exists and user has access to it
    let checkQuery = `
      SELECT pvp.*, p.CompanyId as PlazaCompanyId
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      WHERE pvp.Id = @id
    `;
    const checkParams = { id };

    // Apply role-based filtering for access check
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        checkQuery += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        checkQuery += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      }
    }

    const checkResult = await db.query(checkQuery, checkParams);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Plaza valet point not found or access denied'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user.id;

    // Use stored procedure to update valet point
    const result = await db.query(`
      EXEC [dbo].[sp_PlazaValetPoint_Update]
        @Id = @Id,
        @PlazaId = @PlazaId,
        @ValetPointName = @ValetPointName,
        @IsActive = @IsActive,
        @ModifiedBy = @ModifiedBy,
        @CompanyId = @CompanyId,
        @Latitude = @Latitude,
        @Longitude = @Longitude;
    `, {
      Id: id,
      PlazaId,
      ValetPointName,
      IsActive: IsActive ? 1 : 0,
      ModifiedBy: modifiedBy,
      CompanyId,
      Latitude: Latitude || null,
      Longitude: Longitude || null
    });

    res.json({
      success: true,
      message: 'Plaza valet point updated successfully'
    });
  } catch (error) {
    console.error('Error in updatePlazaValetPoint controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Delete plaza valet point (soft delete)
exports.deletePlazaValetPoint = async (req, res) => {
  try {
    const id = req.params.id;

    // Check if valet point exists and user has access to it
    let checkQuery = `
      SELECT pvp.*, p.CompanyId as PlazaCompanyId
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      WHERE pvp.Id = @id
    `;
    const checkParams = { id };

    // Apply role-based filtering for access check
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        checkQuery += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        checkQuery += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      }
    }

    const checkResult = await db.query(checkQuery, checkParams);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Plaza valet point not found or access denied'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user.id;

    // Use stored procedure to soft delete valet point
    const result = await db.query(`
      EXEC [dbo].[sp_PlazaValetPoint_SoftDelete]
        @Id = @Id,
        @ModifiedBy = @ModifiedBy;
    `, {
      Id: id,
      ModifiedBy: modifiedBy
    });

    res.json({
      success: true,
      message: 'Plaza valet point deleted successfully'
    });
  } catch (error) {
    console.error('Error in deletePlazaValetPoint controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Toggle plaza valet point active status
exports.toggleActiveStatus = async (req, res) => {
  try {
    const id = req.params.id;

    // Check if valet point exists and user has access to it
    let checkQuery = `
      SELECT pvp.*, p.CompanyId as PlazaCompanyId
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      WHERE pvp.Id = @id
    `;
    const checkParams = { id };

    // Apply role-based filtering for access check
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        checkQuery += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        checkQuery += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        checkParams.userId = req.user.id;
      }
    }

    const checkResult = await db.query(checkQuery, checkParams);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Plaza valet point not found or access denied'
      });
    }

    const currentValetPoint = checkResult.recordset[0];
    const newStatus = !currentValetPoint.IsActive;
    const modifiedBy = req.user.id;

    console.log('PlazaValetPointController - Toggle status params:', {
      id: id,
      idType: typeof id,
      currentStatus: currentValetPoint.IsActive,
      newStatus: newStatus,
      newStatusBit: newStatus ? 1 : 0,
      modifiedBy: modifiedBy,
      modifiedByType: typeof modifiedBy
    });

    // Update the status
    await db.query(`
      UPDATE PlazaValetPoint
      SET IsActive = @newStatus, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
      WHERE Id = @id
    `, {
      id: parseInt(id),
      newStatus: newStatus ? 1 : 0,
      modifiedBy: parseInt(modifiedBy)
    });

    res.json({
      success: true,
      message: `Plaza valet point ${newStatus ? 'activated' : 'deactivated'} successfully`,
      isActive: newStatus
    });
  } catch (error) {
    console.error('Error in toggleActiveStatus controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get valet points by plaza ID
exports.getValetPointsByPlaza = async (req, res) => {
  try {
    const plazaId = req.params.plazaId;
    const { isActive } = req.query;

    let query = `
      SELECT
        pvp.Id,
        pvp.PlazaId,
        pvp.ValetPointName,
        pvp.IsActive,
        pvp.CreatedBy,
        pvp.CreatedOn,
        pvp.ModifiedBy,
        pvp.ModifiedOn,
        pvp.CompanyId,
        pvp.Latitude,
        pvp.Longitude,
        p.PlazaName,
        p.PlazaCode,
        c.CompanyName
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE pvp.PlazaId = @plazaId
    `;
    const queryParams = { plazaId };

    // Apply role-based filtering
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        query += ` AND pvp.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        query += ` AND pvp.PlazaId IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    if (isActive !== undefined) {
      query += ` AND pvp.IsActive = @isActive`;
      queryParams.isActive = isActive === 'true' ? 1 : 0;
    }

    query += ` ORDER BY pvp.ValetPointName`;

    const result = await db.query(query, queryParams);

    res.json({
      success: true,
      valetPoints: result.recordset,
      message: 'Plaza valet points retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getValetPointsByPlaza controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

import { useState, useEffect } from 'react';
import { X, MapPin } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';

export default function PlazaValetPointDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  title,
  companies = [],
  plazas = [],
  isLoading = false
}) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    PlazaId: '',
    ValetPointName: '',
    CompanyId: '',
    Latitude: '',
    Longitude: '',
    IsActive: true
  });
  const [errors, setErrors] = useState({});
  const [filteredPlazas, setFilteredPlazas] = useState([]);

  // Reset form when dialog opens/closes or initialData changes
  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        setFormData({
          PlazaId: initialData.PlazaId || '',
          ValetPointName: initialData.ValetPointName || '',
          CompanyId: initialData.CompanyId || '',
          Latitude: initialData.Latitude || '',
          Longitude: initialData.Longitude || '',
          IsActive: initialData.IsActive !== undefined ? initialData.IsActive : true
        });
      } else {
        resetForm();
      }
      setErrors({});
    }
  }, [isOpen, initialData]);

  // Filter plazas based on selected company
  useEffect(() => {
    if (formData.CompanyId) {
      const filtered = plazas.filter(plaza => plaza.CompanyId === formData.CompanyId);
      setFilteredPlazas(filtered);

      // If current plaza doesn't belong to selected company, reset plaza selection
      if (formData.PlazaId && !filtered.find(p => p.Id === formData.PlazaId)) {
        setFormData(prev => ({ ...prev, PlazaId: '' }));
      }
    } else {
      setFilteredPlazas([]);
      setFormData(prev => ({ ...prev, PlazaId: '' }));
    }
  }, [formData.CompanyId, plazas, formData.PlazaId]);

  const resetForm = () => {
    setFormData({
      PlazaId: '',
      ValetPointName: '',
      CompanyId: '',
      Latitude: '',
      Longitude: '',
      IsActive: true
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.CompanyId) newErrors.CompanyId = 'Company is required';
    if (!formData.PlazaId) newErrors.PlazaId = 'Plaza is required';
    if (!formData.ValetPointName.trim()) newErrors.ValetPointName = 'Valet point name is required';
    
    // Validate coordinates if provided
    if (formData.Latitude && (isNaN(formData.Latitude) || formData.Latitude < -90 || formData.Latitude > 90)) {
      newErrors.Latitude = 'Latitude must be a number between -90 and 90';
    }
    if (formData.Longitude && (isNaN(formData.Longitude) || formData.Longitude < -180 || formData.Longitude > 180)) {
      newErrors.Longitude = 'Longitude must be a number between -180 and 180';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Create a copy of the form data
      const dataToSubmit = { ...formData };

      // Add current user ID for tracking who created/modified the record
      if (user && user.id) {
        if (initialData) {
          // For updates, set ModifiedBy
          dataToSubmit.ModifiedBy = user.id;
        } else {
          // For new records, set CreatedBy
          dataToSubmit.CreatedBy = user.id;
        }
      }

      // Convert coordinates to numbers if provided
      if (dataToSubmit.Latitude) {
        dataToSubmit.Latitude = parseFloat(dataToSubmit.Latitude);
      }
      if (dataToSubmit.Longitude) {
        dataToSubmit.Longitude = parseFloat(dataToSubmit.Longitude);
      }

      onSubmit(dataToSubmit);
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setFormData(prev => ({
            ...prev,
            Latitude: position.coords.latitude.toFixed(6),
            Longitude: position.coords.longitude.toFixed(6)
          }));
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get current location. Please enter coordinates manually.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Company Selection */}
            <div>
              <label htmlFor="CompanyId" className="block text-sm font-medium text-gray-700">
                Company *
              </label>
              <select
                id="CompanyId"
                name="CompanyId"
                value={formData.CompanyId}
                onChange={handleInputChange}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  errors.CompanyId ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={user?.role === 'PlazaManager'}
              >
                <option value="">Select Company</option>
                {companies.map((company) => (
                  <option key={company.Id} value={company.Id}>
                    {company.CompanyName}
                  </option>
                ))}
              </select>
              {errors.CompanyId && <p className="mt-1 text-sm text-red-600">{errors.CompanyId}</p>}
            </div>

            {/* Plaza Selection */}
            <div>
              <label htmlFor="PlazaId" className="block text-sm font-medium text-gray-700">
                Plaza *
              </label>
              <select
                id="PlazaId"
                name="PlazaId"
                value={formData.PlazaId}
                onChange={handleInputChange}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  errors.PlazaId ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={!formData.CompanyId}
              >
                <option value="">Select Plaza</option>
                {filteredPlazas.map((plaza) => (
                  <option key={plaza.Id} value={plaza.Id}>
                    {plaza.PlazaName} ({plaza.PlazaCode})
                  </option>
                ))}
              </select>
              {errors.PlazaId && <p className="mt-1 text-sm text-red-600">{errors.PlazaId}</p>}
            </div>
          </div>

          {/* Valet Point Name */}
          <div>
            <label htmlFor="ValetPointName" className="block text-sm font-medium text-gray-700">
              Valet Point Name *
            </label>
            <input
              type="text"
              id="ValetPointName"
              name="ValetPointName"
              value={formData.ValetPointName}
              onChange={handleInputChange}
              className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                errors.ValetPointName ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter valet point name"
            />
            {errors.ValetPointName && <p className="mt-1 text-sm text-red-600">{errors.ValetPointName}</p>}
          </div>

          {/* Location Coordinates */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Location Coordinates (Optional)
              </label>
              <button
                type="button"
                onClick={getCurrentLocation}
                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <MapPin size={14} className="mr-1" />
                Get Current Location
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="Latitude" className="block text-sm font-medium text-gray-700">
                  Latitude
                </label>
                <input
                  type="number"
                  id="Latitude"
                  name="Latitude"
                  value={formData.Latitude}
                  onChange={handleInputChange}
                  step="any"
                  min="-90"
                  max="90"
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.Latitude ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="e.g., 40.712776"
                />
                {errors.Latitude && <p className="mt-1 text-sm text-red-600">{errors.Latitude}</p>}
              </div>
              <div>
                <label htmlFor="Longitude" className="block text-sm font-medium text-gray-700">
                  Longitude
                </label>
                <input
                  type="number"
                  id="Longitude"
                  name="Longitude"
                  value={formData.Longitude}
                  onChange={handleInputChange}
                  step="any"
                  min="-180"
                  max="180"
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    errors.Longitude ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="e.g., -74.005974"
                />
                {errors.Longitude && <p className="mt-1 text-sm text-red-600">{errors.Longitude}</p>}
              </div>
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              id="IsActive"
              name="IsActive"
              type="checkbox"
              checked={formData.IsActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="IsActive" className="ml-2 block text-sm text-gray-900">
              Active
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {initialData ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                initialData ? 'Update' : 'Create'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
